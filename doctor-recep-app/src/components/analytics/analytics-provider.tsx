'use client'

import { useEffect } from 'react'
import { usePathname } from 'next/navigation'
import { initializeAnalytics, trackPageView } from '@/lib/analytics'

/**
 * Analytics Provider Component
 * Handles client-side analytics initialization and page tracking
 * Respects the two-zone security model
 * Uses lazy initialization to avoid blocking page load
 */
export function AnalyticsProvider({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()

  useEffect(() => {
    // Lazy initialize analytics after page is fully loaded
    const timer = setTimeout(() => {
      initializeAnalytics()
    }, 100) // Small delay to ensure page is interactive

    return () => clearTimeout(timer)
  }, [])

  useEffect(() => {
    // Lazy track page changes with debouncing
    const timer = setTimeout(() => {
      trackPageView(document.title)
    }, 50)

    return () => clearTimeout(timer)
  }, [pathname])

  return <>{children}</>
}
