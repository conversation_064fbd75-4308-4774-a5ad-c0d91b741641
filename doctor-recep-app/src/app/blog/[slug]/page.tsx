import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { getBlogPost, getBlogPosts, urlFor } from '@/lib/sanity/client'
import { PortableTextRenderer } from '@/components/sanity/portable-text-renderer'
import { Calendar, Clock, ArrowLeft, Share2 } from 'lucide-react'

interface BlogPostPageProps {
  params: Promise<{ slug: string }>
}

// Generate static params for ISR
export async function generateStaticParams() {
  const posts = await getBlogPosts()
  return posts.map((post) => ({
    slug: post.slug.current,
  }))
}

// Generate metadata for SEO
export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const { slug } = await params
  const post = await getBlogPost(slug)

  if (!post) {
    return {
      title: 'Post Not Found - Celer AI',
      description: 'The requested blog post could not be found.',
    }
  }

  return {
    title: post.seo?.title || `${post.title} - Celer AI Blog`,
    description: post.seo?.description || post.excerpt || `Read ${post.title} on the Celer AI blog`,
    keywords: post.seo?.keywords?.join(', ') || 'healthcare, AI, medical documentation',
    openGraph: {
      title: post.title,
      description: post.excerpt || '',
      type: 'article',
      publishedTime: post.publishedAt,
      authors: post.author?.name ? [post.author.name] : undefined,
      images: post.mainImage ? [
        {
          url: urlFor(post.mainImage).width(1200).height(630).fit('crop').auto('format').url(),
          width: 1200,
          height: 630,
          alt: post.mainImage.alt || post.title,
        }
      ] : undefined,
    },
    twitter: {
      card: 'summary_large_image',
      title: post.title,
      description: post.excerpt || '',
      images: post.mainImage ? [
        urlFor(post.mainImage).width(1200).height(630).fit('crop').auto('format').url()
      ] : undefined,
    },
  }
}

// Enable ISR with 1 hour revalidation
export const revalidate = 3600

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const { slug } = await params
  const post = await getBlogPost(slug)

  if (!post) {
    notFound()
  }

  const publishedDate = new Date(post.publishedAt).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })

  // Estimate read time (average 200 words per minute)
  const estimatedReadTime = post.body 
    ? Math.ceil(JSON.stringify(post.body).length / 1000) 
    : 5

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-indigo-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-slate-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">C</span>
              </div>
              <span className="text-xl font-bold text-slate-900">Celer AI</span>
            </Link>
            <nav className="hidden md:flex items-center space-x-6">
              <Link href="/" className="text-slate-600 hover:text-slate-900 transition-colors">
                Home
              </Link>
              <Link href="/blog" className="text-indigo-600 font-medium">
                Blog
              </Link>
              <Link href="/guide" className="text-slate-600 hover:text-slate-900 transition-colors">
                Guides
              </Link>
              <Link href="/login" className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-4 py-2 rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all">
                Login
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Back to Blog */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pt-8">
        <Link
          href="/blog"
          className="inline-flex items-center space-x-2 text-indigo-600 hover:text-indigo-800 transition-colors group"
        >
          <ArrowLeft className="w-4 h-4 group-hover:-translate-x-1 transition-transform" />
          <span>Back to Blog</span>
        </Link>
      </div>

      {/* Article */}
      <article className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Categories */}
        {post.categories && post.categories.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-6">
            {post.categories.map((category) => (
              <span
                key={category.slug.current}
                className="px-3 py-1 bg-indigo-100 text-indigo-700 text-sm font-medium rounded-full"
              >
                {category.title}
              </span>
            ))}
          </div>
        )}

        {/* Title */}
        <h1 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6 leading-tight">
          {post.title}
        </h1>

        {/* Meta Information */}
        <div className="flex flex-wrap items-center gap-6 text-slate-600 mb-8 pb-8 border-b border-slate-200">
          <div className="flex items-center space-x-2">
            <Calendar className="w-5 h-5" />
            <span>{publishedDate}</span>
          </div>
          <div className="flex items-center space-x-2">
            <Clock className="w-5 h-5" />
            <span>{estimatedReadTime} min read</span>
          </div>
          {post.author && (
            <div className="flex items-center space-x-3">
              {post.author.image && (
                <Image
                  src={urlFor(post.author.image).width(40).height(40).fit('crop').auto('format').url()}
                  alt={post.author.name}
                  width={40}
                  height={40}
                  className="rounded-full"
                />
              )}
              <span className="font-medium">{post.author.name}</span>
            </div>
          )}
        </div>

        {/* Featured Image */}
        {post.mainImage && (
          <div className="relative h-64 md:h-96 rounded-2xl overflow-hidden mb-8 shadow-lg">
            <Image
              src={urlFor(post.mainImage).width(1200).height(600).fit('crop').auto('format').url()}
              alt={post.mainImage.alt || post.title}
              fill
              className="object-cover"
              priority
            />
          </div>
        )}

        {/* Content */}
        <div className="prose prose-lg prose-slate max-w-none">
          <PortableTextRenderer content={post.body} />
        </div>

        {/* Share Section */}
        <div className="mt-12 pt-8 border-t border-slate-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-slate-900 mb-2">
                Found this helpful?
              </h3>
              <p className="text-slate-600">
                Share it with your colleagues in healthcare
              </p>
            </div>
            <button
              onClick={() => {
                if (navigator.share) {
                  navigator.share({
                    title: post.title,
                    text: post.excerpt || '',
                    url: window.location.href,
                  })
                } else {
                  navigator.clipboard.writeText(window.location.href)
                  alert('Link copied to clipboard!')
                }
              }}
              className="flex items-center space-x-2 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-6 py-3 rounded-lg transition-all transform hover:scale-105"
            >
              <Share2 className="w-5 h-5" />
              <span>Share</span>
            </button>
          </div>
        </div>
      </article>
    </div>
  )
}
