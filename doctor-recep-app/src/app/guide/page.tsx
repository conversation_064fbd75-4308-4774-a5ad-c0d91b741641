import { Metadata } from 'next'
import Link from 'next/link'
import Image from 'next/image'
import { getGuidePosts, urlFor } from '@/lib/sanity/client'
import { BookO<PERSON>, Clock, ArrowRight, Star } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Guides - Celer AI',
  description: 'Step-by-step guides for healthcare professionals using AI documentation tools',
  keywords: 'healthcare guides, AI documentation, medical tutorials, how-to',
}

// Enable ISR with 1 hour revalidation
export const revalidate = 3600

export default async function GuidePage() {
  const guides = await getGuidePosts()

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-emerald-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-slate-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">C</span>
              </div>
              <span className="text-xl font-bold text-slate-900">Celer AI</span>
            </Link>
            <nav className="hidden md:flex items-center space-x-6">
              <Link href="/" className="text-slate-600 hover:text-slate-900 transition-colors">
                Home
              </Link>
              <Link href="/blog" className="text-slate-600 hover:text-slate-900 transition-colors">
                Blog
              </Link>
              <Link href="/guide" className="text-emerald-600 font-medium">
                Guides
              </Link>
              <Link href="/login" className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-4 py-2 rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all">
                Login
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
            Healthcare AI <span className="text-transparent bg-clip-text bg-gradient-to-r from-emerald-600 to-teal-600">Guides</span>
          </h1>
          <p className="text-xl text-slate-600 leading-relaxed">
            Step-by-step tutorials to help you master AI-powered healthcare documentation
          </p>
        </div>
      </section>

      {/* Guides Grid */}
      <section className="pb-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          {guides.length === 0 ? (
            <div className="text-center py-16">
              <div className="w-24 h-24 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <BookOpen className="w-12 h-12 text-slate-400" />
              </div>
              <h3 className="text-xl font-semibold text-slate-900 mb-2">No guides yet</h3>
              <p className="text-slate-600">Check back soon for comprehensive tutorials and guides!</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {guides.map((guide) => (
                <GuideCard key={guide._id} guide={guide} />
              ))}
            </div>
          )}
        </div>
      </section>
    </div>
  )
}

function GuideCard({ guide }: { guide: any }) {
  const publishedDate = new Date(guide.publishedAt).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })

  const difficultyColors = {
    beginner: 'bg-green-100 text-green-700',
    intermediate: 'bg-yellow-100 text-yellow-700',
    advanced: 'bg-red-100 text-red-700'
  }

  const handleClick = () => {
    // Track guide view in public zone
    if (typeof window !== 'undefined') {
      import('@/lib/analytics').then(({ trackEvent }) => {
        trackEvent('guide_viewed', { page_title: guide.title })
      })
    }
  }

  return (
    <article className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 overflow-hidden group">
      {/* Featured Image */}
      {guide.mainImage && (
        <div className="relative h-48 overflow-hidden">
          <Image
            src={urlFor(guide.mainImage).width(400).height(300).fit('crop').auto('format').url()}
            alt={guide.mainImage.alt || guide.title}
            fill
            className="object-cover group-hover:scale-110 transition-transform duration-300"
            loading="lazy"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
          
          {/* Difficulty Badge */}
          {guide.difficulty && (
            <div className="absolute top-4 left-4">
              <span className={`px-3 py-1 text-xs font-medium rounded-full ${difficultyColors[guide.difficulty as keyof typeof difficultyColors] || difficultyColors.beginner}`}>
                {guide.difficulty}
              </span>
            </div>
          )}
        </div>
      )}

      {/* Content */}
      <div className="p-6">
        {/* Tags */}
        {guide.tags && guide.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-3">
            {guide.tags.slice(0, 2).map((tag: any) => (
              <span
                key={tag.slug.current}
                className="px-3 py-1 bg-emerald-100 text-emerald-700 text-xs font-medium rounded-full"
              >
                {tag.title}
              </span>
            ))}
          </div>
        )}

        {/* Title */}
        <h2 className="text-xl font-bold text-slate-900 mb-3 line-clamp-2 group-hover:text-emerald-600 transition-colors">
          {guide.title}
        </h2>

        {/* Excerpt */}
        {guide.excerpt && (
          <p className="text-slate-600 mb-4 line-clamp-3 leading-relaxed">
            {guide.excerpt}
          </p>
        )}

        {/* Meta */}
        <div className="flex items-center justify-between text-sm text-slate-500 mb-4">
          <div className="flex items-center space-x-4">
            {guide.estimatedReadTime && (
              <div className="flex items-center space-x-1">
                <Clock className="w-4 h-4" />
                <span>{guide.estimatedReadTime} min</span>
              </div>
            )}
            <div className="flex items-center space-x-1">
              <BookOpen className="w-4 h-4" />
              <span>{publishedDate}</span>
            </div>
          </div>
        </div>

        {/* Read Guide Link */}
        <Link
          href={`/guide/${guide.slug.current}`}
          onClick={handleClick}
          className="inline-flex items-center space-x-2 text-emerald-600 hover:text-emerald-800 font-medium transition-colors group"
        >
          <span>Read Guide</span>
          <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
        </Link>
      </div>
    </article>
  )
}
