import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { getGuidePost, getGuidePosts, urlFor } from '@/lib/sanity/client'
import { PortableTextRenderer } from '@/components/sanity/portable-text-renderer'
import { BookOpen, Clock, ArrowLeft, Share2, Star } from 'lucide-react'

interface GuidePostPageProps {
  params: Promise<{ slug: string }>
}

// Generate static params for ISR
export async function generateStaticParams() {
  const guides = await getGuidePosts()
  return guides.map((guide) => ({
    slug: guide.slug.current,
  }))
}

// Generate metadata for SEO
export async function generateMetadata({ params }: GuidePostPageProps): Promise<Metadata> {
  const { slug } = await params
  const guide = await getGuidePost(slug)

  if (!guide) {
    return {
      title: 'Guide Not Found - Celer AI',
      description: 'The requested guide could not be found.',
    }
  }

  return {
    title: guide.seo?.title || `${guide.title} - Celer AI Guides`,
    description: guide.seo?.description || guide.excerpt || `Learn ${guide.title} with our comprehensive guide`,
    keywords: guide.seo?.keywords?.join(', ') || 'healthcare guides, AI documentation, medical tutorials',
    openGraph: {
      title: guide.title,
      description: guide.excerpt || '',
      type: 'article',
      publishedTime: guide.publishedAt,
      images: guide.mainImage ? [
        {
          url: urlFor(guide.mainImage).width(1200).height(630).fit('crop').auto('format').url(),
          width: 1200,
          height: 630,
          alt: guide.mainImage.alt || guide.title,
        }
      ] : undefined,
    },
  }
}

// Enable ISR with 1 hour revalidation
export const revalidate = 3600

export default async function GuidePostPage({ params }: GuidePostPageProps) {
  const { slug } = await params
  const guide = await getGuidePost(slug)

  if (!guide) {
    notFound()
  }

  const publishedDate = new Date(guide.publishedAt).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })

  const difficultyColors = {
    beginner: 'bg-green-100 text-green-700 border-green-200',
    intermediate: 'bg-yellow-100 text-yellow-700 border-yellow-200',
    advanced: 'bg-red-100 text-red-700 border-red-200'
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-emerald-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-slate-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">C</span>
              </div>
              <span className="text-xl font-bold text-slate-900">Celer AI</span>
            </Link>
            <nav className="hidden md:flex items-center space-x-6">
              <Link href="/" className="text-slate-600 hover:text-slate-900 transition-colors">
                Home
              </Link>
              <Link href="/blog" className="text-slate-600 hover:text-slate-900 transition-colors">
                Blog
              </Link>
              <Link href="/guide" className="text-emerald-600 font-medium">
                Guides
              </Link>
              <Link href="/login" className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-4 py-2 rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all">
                Login
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Back to Guides */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pt-8">
        <Link
          href="/guide"
          className="inline-flex items-center space-x-2 text-emerald-600 hover:text-emerald-800 transition-colors group"
        >
          <ArrowLeft className="w-4 h-4 group-hover:-translate-x-1 transition-transform" />
          <span>Back to Guides</span>
        </Link>
      </div>

      {/* Article */}
      <article className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Guide Meta */}
        <div className="flex flex-wrap items-center gap-4 mb-6">
          {/* Difficulty */}
          {guide.difficulty && (
            <span className={`px-4 py-2 text-sm font-medium rounded-lg border ${difficultyColors[guide.difficulty as keyof typeof difficultyColors] || difficultyColors.beginner}`}>
              {guide.difficulty} level
            </span>
          )}
          
          {/* Tags */}
          {guide.tags && guide.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {guide.tags.slice(0, 3).map((tag) => (
                <span
                  key={tag.slug.current}
                  className="px-3 py-1 bg-emerald-100 text-emerald-700 text-sm font-medium rounded-full"
                >
                  {tag.title}
                </span>
              ))}
            </div>
          )}
        </div>

        {/* Title */}
        <h1 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6 leading-tight">
          {guide.title}
        </h1>

        {/* Meta Information */}
        <div className="flex flex-wrap items-center gap-6 text-slate-600 mb-8 pb-8 border-b border-slate-200">
          <div className="flex items-center space-x-2">
            <BookOpen className="w-5 h-5" />
            <span>{publishedDate}</span>
          </div>
          {guide.estimatedReadTime && (
            <div className="flex items-center space-x-2">
              <Clock className="w-5 h-5" />
              <span>{guide.estimatedReadTime} min read</span>
            </div>
          )}
        </div>

        {/* Featured Image */}
        {guide.mainImage && (
          <div className="relative h-64 md:h-96 rounded-2xl overflow-hidden mb-8 shadow-lg">
            <Image
              src={urlFor(guide.mainImage).width(1200).height(600).fit('crop').auto('format').url()}
              alt={guide.mainImage.alt || guide.title}
              fill
              className="object-cover"
              priority
            />
          </div>
        )}

        {/* Excerpt */}
        {guide.excerpt && (
          <div className="bg-emerald-50 border border-emerald-200 rounded-xl p-6 mb-8">
            <h2 className="text-lg font-semibold text-emerald-900 mb-2">What you&apos;ll learn</h2>
            <p className="text-emerald-800 leading-relaxed">{guide.excerpt}</p>
          </div>
        )}

        {/* Content */}
        <div className="prose prose-lg prose-slate max-w-none">
          <PortableTextRenderer content={guide.body} />
        </div>

        {/* Share Section */}
        <div className="mt-12 pt-8 border-t border-slate-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-slate-900 mb-2">
                Found this guide helpful?
              </h3>
              <p className="text-slate-600">
                Share it with your healthcare team
              </p>
            </div>
            <button
              onClick={() => {
                if (navigator.share) {
                  navigator.share({
                    title: guide.title,
                    text: guide.excerpt || '',
                    url: window.location.href,
                  })
                } else {
                  navigator.clipboard.writeText(window.location.href)
                  alert('Link copied to clipboard!')
                }
              }}
              className="flex items-center space-x-2 bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white px-6 py-3 rounded-lg transition-all transform hover:scale-105"
            >
              <Share2 className="w-5 h-5" />
              <span>Share</span>
            </button>
          </div>
        </div>
      </article>
    </div>
  )
}
